import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MessageList, type MessagesSectionListProps } from 'b-ui-lib';
import { Alert, View } from 'react-native';
import { useState } from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

const SECTIONS = [
  {
    title: '03 Dec 2024',
    data: [
      {
        id: '1a2b3c4d-1234-abcd-5678-ef1234567890',
        messageKey: '001',
        sentDate: '2024-12-03T10:15:00',
        username: 'Admin',
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Meeting Reminder: Budget Review',
        body: 'Hi <PERSON>, Just a reminder about tomorrow meeting to review the budget. Please make sure to bring the updated figures. Thanks, Alice.',
        avatarName: 'A',
        isFlagged: true,
        attachmentsCount: 3,
        hasReplies: true,
        hasAttachments: true,
        hasComments: true,
        hasMetadata: true,
        hasFolders: true,
        hasCases: true,
        inOut: 1,
        type: 'Email',
        isViewed: false,
      },
      {
        id: '2b3c4d5e-2345-bcde-6789-f01234567891',
        messageKey: '002',
        sentDate: '2024-12-03T09:30:00',
        username: 'Admin',
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Your Support Request (#12345)',
        body: 'Hello Support Team, Thank you for reaching out. We are reviewing your request and will get back to you within 24 hours.',
        avatarName: 'J',
        isFlagged: false,
        attachmentsCount: 0,
        hasReplies: false,
        hasAttachments: false,
        hasComments: false,
        hasMetadata: true,
        hasFolders: false,
        hasCases: true,
        inOut: 2,
        type: 'Email',
        isViewed: true,
      },
      {
        id: '3c4d5e6f-3456-cdef-7890-123456789012',
        messageKey: '003',
        sentDate: '2024-12-03T08:15:00',
        username: 'Admin',
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'December Newsletter: Top Stories',
        body: 'Hello Subscriber, This month, we are sharing the top stories in technology, health, and lifestyle. Do not miss out!',
        avatarName: 'N',
        isFlagged: false,
        attachmentsCount: 1,
        hasReplies: false,
        hasAttachments: true,
        hasComments: false,
        hasMetadata: false,
        hasFolders: false,
        hasCases: false,
        inOut: 2,
        type: 'Email',
        isViewed: true,
      },
    ],
  },
];

const meta = {
  title: 'Message List/With Swipe Gestures',
  component: MessageList,
  argTypes: {
    sections: { control: 'object', description: 'Sections' },
    emptyText: {
      control: 'text',
      description: 'Empty message when empty list',
    },
    isSkeletonLoading: {
      control: 'boolean',
      description: 'Is skeleton loading',
    },
    isMultiSelectActive: {
      control: 'boolean',
      description: 'If the multiselect of section list is active',
    },
    selectedMessagesIds: {
      control: 'object',
      description: 'Ids of selected messages',
    },
    handleTapMessage: {
      action: 'Tap to select email',
      description: 'Tap to select email',
    },
    handleLongTapToSelectEmail: {
      action: 'Long tap to select email',
      description: 'Long tap to select email',
    },
    handleTapToSelectAdditionalEmail: {
      action: 'Tap to select email',
      description: 'Tap to select email',
    },
    handleDeselectMessage: {
      action: 'Deselect email',
      description: 'Deselect email',
    },
    handleFlagPress: {
      action: 'Handle flag pressed',
      description: "Message's flag icon pressed",
    },
    onReply: {
      action: 'Reply to message',
      description: 'Called when user swipes right on a message',
    },
    onDelete: {
      action: 'Delete message',
      description: 'Called when user swipes left on a message',
    },
  },
  args: {
    initialNumToRender: 10,
    loadMoreEmails: () => {},
    listFooterComponent: undefined,
    sections: [],
    emptyText: '',
    isSkeletonLoading: false,
    isMultiSelectActive: false,
    selectedMessagesIds: [],
    handleTapMessage: () => {},
    handleLongTapToSelectEmail: () => {},
    handleTapToSelectAdditionalEmail: () => {},
    handleDeselectMessage: () => {},
    handleFlagPress: () => {},
    onReply: () => {},
    onDelete: () => {},
  },
  decorators: [
    (Story) => (
      <GestureHandlerRootView style={{ flex: 1 }}>
        <View
          style={{
            flex: 1,
            backgroundColor: '#f2f2f3',
          }}
        >
          <Story />
        </View>
      </GestureHandlerRootView>
    ),
  ],
} satisfies Meta<typeof MessageList>;

export default meta;
type Story = StoryObj<typeof meta>;

const MessagesSectionListWithSwipe = (props: MessagesSectionListProps) => {
  const [isMultiSelectActive, setIsMultiSelectActive] =
    useState<boolean>(false);
  const [selectedMessagesIds, setSelectedMessagesIds] = useState<string[]>([]);

  const handleLongTapToSelectEmail = (messageId: string) => {
    setSelectedMessagesIds([...selectedMessagesIds, messageId]);
    setIsMultiSelectActive(true);
  };

  const handleTapToSelectAdditionalEmail = (messageId: string) => {
    setSelectedMessagesIds([...selectedMessagesIds, messageId]);
  };

  const handleDeselectMessage = (messageId: string) => {
    const newSelectedMessagesIds = selectedMessagesIds.filter(
      (id) => id !== messageId
    );

    setSelectedMessagesIds(newSelectedMessagesIds);
  };

  const handleFlagPress = () => {
    Alert.alert('Flag Pressed', 'Message flag toggled');
  };

  const handleReply = (message: any) => {
    Alert.alert(
      'Reply Action',
      `Replying to message: "${message.subject}"\n\nThe message item stayed stationary while the green reply background was revealed behind it.`
    );
  };

  const handleDelete = (messageIds: string[]) => {
    Alert.alert(
      'Delete Action',
      `Deleting messages with IDs: ${messageIds.join(', ')}\n\nThis demonstrates the flexible left swipe action.`
    );
  };

  return (
    <MessageList
      {...props}
      sections={SECTIONS}
      emptyText=""
      isSkeletonLoading={false}
      isMultiSelectActive={isMultiSelectActive}
      selectedMessagesIds={selectedMessagesIds}
      handleTapMessage={() => {}}
      handleLongTapToSelectEmail={handleLongTapToSelectEmail}
      handleTapToSelectAdditionalEmail={handleTapToSelectAdditionalEmail}
      handleDeselectMessage={handleDeselectMessage}
      handleFlagPress={handleFlagPress}
      onReply={handleReply}
      onDelete={handleDelete}
    />
  );
};

export const WithSwipeGestures: Story = {
  args: {
    sections: SECTIONS,
    emptyText: '',
    isSkeletonLoading: false,
    isMultiSelectActive: false,
    selectedMessagesIds: [],
    handleTapMessage: () => {},
    handleLongTapToSelectEmail: () => {},
    handleTapToSelectAdditionalEmail: () => {},
    handleDeselectMessage: () => {},
    handleFlagPress: () => {},
    onReply: () => {},
    onDelete: () => {},
  },
  render: (args) => <MessagesSectionListWithSwipe {...args} />,
};
