import { useState } from 'react';
import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import { SearchInput, type SearchInputProps } from 'b-ui-lib';

const meta = {
  title: 'Search Input',
  component: SearchInput,
  argTypes: {
    searchInputRef: { control: 'object', description: 'Ref' },
    value: { control: 'text', description: 'Input value' },
    placeholder: { control: 'text', description: 'Input placeholder' },
    onChangeText: {
      action: 'Change input text',
      description: 'Handler for when the input changes',
    },
    handleDebounceFunction: {
      action: 'Handle debounce function',
      description: 'Handle debounce function',
    },
    debounceWait: { control: 'number', description: 'Debounce wait number' },
    handleInputClear: {
      action: 'Clear input text',
      description: 'Handler for when clear input text',
    },
    containerStyle: { control: 'object', description: 'Styles' },
    testID: { control: 'text', description: 'testID' },
  },
  args: {
    searchInputRef: undefined,
    value: '',
    placeholder: 'Company Prefix',
    onChangeText: () => {},
    handleDebounceFunction: () => {},
    debounceWait: 500,
    handleInputClear: () => {},
    containerStyle: {},
    testID: '',
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20, backgroundColor: '#f2f2f3' }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof SearchInput>;

export default meta;

type Story = StoryObj<typeof meta>;

const SearchInputWithHooks = (props: SearchInputProps) => {
  const [inputValue, setInputValue] = useState<string>('');

  const handleSetValue = (textValue: string) => setInputValue(textValue);
  const handleInputClear = () => setInputValue('');

  const handleDebounceFunction = (
    searchText: string,
    otherDebounceParams?: {}
  ) => {
    console.log({ searchText });
    console.log({ otherDebounceParams });
  };

  return (
    <SearchInput
      {...props}
      value={inputValue}
      onChangeText={handleSetValue}
      handleDebounceFunction={handleDebounceFunction}
      handleInputClear={handleInputClear}
      debounceParameters={{ someValue: 'test' }}
    />
  );
};

export const Basic: Story = {
  args: {
    value: '',
    onChangeText: () => {},
    handleInputClear: () => {},
    placeholder: 'Company Prefix',
  },
  render: (args) => <SearchInputWithHooks {...args} />,
};
