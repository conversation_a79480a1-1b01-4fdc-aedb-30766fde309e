import React from 'react';
import {
  type TextStyle,
  type ViewStyle,
  type GestureResponderEvent,
  StyleSheet,
  Pressable,
  ActivityIndicator,
  type PressableProps,
} from 'react-native';
import {
  type ColorTheme,
  type Theme,
  CustomText,
  FONT_SIZES,
  FONT_WEIGHTS,
  SPACING,
  useThemeAwareObject,
} from 'b-ui-lib';

export const BUTTON_DEFAULT_VARIANTS = {
  primary: 'primary',
  secondary: 'secondary',
  secondaryWithGrayBackground: 'secondaryWithGrayBackground',
};

type Props = PressableProps & {
  variant?: (typeof BUTTON_DEFAULT_VARIANTS)[keyof typeof BUTTON_DEFAULT_VARIANTS];
  title: string;
  onPress: (event: GestureResponderEvent) => void;
  containerStyle?: ViewStyle;
  textStyle?: TextStyle;
  isLoading?: boolean;
  loaderColor?: string;
  isDisabled?: boolean;
  testID?: string;
};

export const Button: React.FC<Props> = ({
  variant = BUTTON_DEFAULT_VARIANTS.primary,
  title,
  onPress,
  containerStyle,
  textStyle,
  isLoading,
  loaderColor,
  isDisabled,
  testID,
  ...restProps
}) => {
  const { styles, color } = useThemeAwareObject((theme) =>
    createStyles(theme, variant, isDisabled)
  );

  return (
    <Pressable
      {...restProps}
      testID={testID}
      style={[styles.container, containerStyle]}
      onPress={!isDisabled ? onPress : null}
    >
      <CustomText style={[styles.text, textStyle]}>
        {isLoading ? (
          <ActivityIndicator size="small" color={loaderColor ?? color.BLACK} />
        ) : (
          title
        )}
      </CustomText>
    </Pressable>
  );
};

const BUTTON_STYLES = {
  [BUTTON_DEFAULT_VARIANTS.primary]: {
    backgroundColor: (color: ColorTheme) => color.BRAND_DEFAULT,
    color: (color: ColorTheme) => color.TEXT_DEFAULT,
    fontWeight: () => FONT_WEIGHTS.FIVE_HUNDRED,
    fontSize: () => FONT_SIZES.FOURTEEN,
  },
  [BUTTON_DEFAULT_VARIANTS.secondary]: {
    backgroundColor: () => 'transparent',
    color: (color: ColorTheme) => color.BRAND_BLUE,
    fontWeight: () => FONT_WEIGHTS.NORMAL,
    fontSize: () => FONT_SIZES.TWELVE,
  },
  [BUTTON_DEFAULT_VARIANTS.secondaryWithGrayBackground]: {
    backgroundColor: (color: ColorTheme) => color.TAB_BACKGROUND,
    color: (color: ColorTheme) => color.MESSAGE_FLAG,
    fontWeight: () => FONT_WEIGHTS.FIVE_HUNDRED,
    fontSize: () => FONT_SIZES.TWELVE,
  },
};

const createStyles = (
  { color }: Theme,
  variant: (typeof BUTTON_DEFAULT_VARIANTS)[keyof typeof BUTTON_DEFAULT_VARIANTS],
  isDisabled?: boolean
) => {
  const styles = StyleSheet.create({
    container: {
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: BUTTON_STYLES[variant]?.backgroundColor?.(color),
      paddingVertical: SPACING.FOURTEEN,
      paddingHorizontal: SPACING.M,
      borderRadius: SPACING.XS,
      gap: 10,
      minWidth: 80,
      opacity: isDisabled ? 0.5 : 1,
    },
    text: {
      color: BUTTON_STYLES[variant]?.color?.(color),
      textAlign: 'center',
      fontWeight: BUTTON_STYLES[variant]?.fontWeight?.(),
      fontSize: BUTTON_STYLES[variant]?.fontSize?.(),
    },
  });

  return { styles, color };
};
