import { type FC } from 'react';
import {
  type ViewStyle,
  type PressableProps,
  Pressable,
  StyleSheet,
  View,
} from 'react-native';
import {
  type Theme,
  CustomText,
  FONT_SIZES,
  IconButton,
  SPACING,
  useThemeAwareObject,
} from 'b-ui-lib';

export type ComposeMessageInputProps = PressableProps & {
  selectedEmail?: string;
  isBottomSheetOpen?: boolean;
  handleArrowPress?: () => void;
  containerStyle?: ViewStyle;
  title: string;
};

export const ComposeMessageInput: FC<ComposeMessageInputProps> = ({
  title,
  selectedEmail,
  isBottomSheetOpen,
  handleArrowPress,
  containerStyle,
  ...restProps
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <Pressable
      {...restProps}
      style={[styles.container, containerStyle]}
      hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
      onPress={handleArrowPress}
    >
      <View style={styles.textContainer}>
        <CustomText style={styles.fromText}>{title}</CustomText>
        {selectedEmail && (
          <CustomText style={styles.selectedMessageText}>
            {selectedEmail}
          </CustomText>
        )}
      </View>

      <IconButton
        name={!isBottomSheetOpen ? 'chevron-up' : 'chevron-down'}
        size={16}
        hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
        color={color.ARROW_EXPAND}
        onPress={handleArrowPress}
      />
    </Pressable>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.S,
      paddingHorizontal: SPACING.TEN,
      gap: SPACING.XS,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
    },
    textContainer: {
      gap: SPACING.XS,
    },
    fromText: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.TEXT_DEFAULT,
    },
    selectedMessageText: {
      fontSize: FONT_SIZES.FOURTEEN,
      color: color.MESSAGE_FLAG,
    },
  });

  return { styles, color };
};
