import { type FC, type RefObject, type ReactNode, useCallback } from 'react';
import {
  BackHandler,
  StyleSheet,
  type ViewStyle,
  Platform,
  View,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import BottomSheet, {
  type BottomSheetBackdropProps,
  type BottomSheetProps,
  BottomSheetBackdrop,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import type { BottomSheetMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import { type Theme, useThemeAwareObject } from 'b-ui-lib';

export type CustomBottomSheetProps = BottomSheetProps & {
  bottomSheetRef?: RefObject<BottomSheetMethods> | null;
  handleSheetChanges?: (index: number) => void;
  handleBackPress?: () => boolean;
  backPressDependencies?: [];
  snapPoints?: string[];
  hasBackdrop?: boolean;
  handleBackdropPress?: () => void; // This is used for additional actions. Bottom sheet is closing by default.
  useScrollView?: boolean; // Controls whether to use BottomSheetScrollView or regular View
  style?: {
    bottomSheet: ViewStyle;
    bottomSheetIndicator: ViewStyle;
    scrollView: ViewStyle;
    scrollViewContentContainer: ViewStyle;
  } | null;
  children?: ReactNode;
};

export const CustomBottomSheet: FC<CustomBottomSheetProps> = ({
  bottomSheetRef,
  handleSheetChanges,
  handleBackPress,
  backPressDependencies = [],
  snapPoints,
  hasBackdrop = false,
  handleBackdropPress,
  useScrollView = true,
  style,
  children,
  ...restBottomSheetProps
}) => {
  const { styles } = useThemeAwareObject(createStyles);

  // handleBackPress() should return boolean
  useFocusEffect(
    useCallback(() => {
      const backHandler = handleBackPress
        ? BackHandler.addEventListener('hardwareBackPress', handleBackPress)
        : null;

      return () => {
        backHandler && backHandler.remove();
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [handleBackPress, backPressDependencies])
  );

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        onPress={handleBackdropPress && handleBackdropPress}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
      />
    ),
    [handleBackdropPress]
  );

  return (
    <BottomSheet
      {...restBottomSheetProps}
      snapPoints={snapPoints ?? []}
      ref={bottomSheetRef}
      index={-1}
      onChange={handleSheetChanges && handleSheetChanges}
      enablePanDownToClose
      enableHandlePanningGesture
      enableContentPanningGesture
      activeOffsetY={Platform.OS === 'android' ? [-1, 1] : undefined}
      failOffsetX={Platform.OS === 'android' ? [-5, 5] : undefined}
      style={[styles.bottomSheetStyle, style?.bottomSheet]}
      handleIndicatorStyle={[
        styles.bottomSheetIndicatorStyle,
        style?.bottomSheetIndicator,
      ]}
      handleStyle={[styles.bottomSheetStyle, style?.bottomSheet]}
      backdropComponent={hasBackdrop ? renderBackdrop : null}
    >
      {useScrollView ? (
        <BottomSheetScrollView
          style={[styles.bottomSheetScrollView, style?.scrollView]}
          contentContainerStyle={style?.scrollViewContentContainer}
        >
          {children}
        </BottomSheetScrollView>
      ) : (
        <View style={[styles.bottomSheetScrollView, style?.scrollView]}>
          {children}
        </View>
      )}
    </BottomSheet>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    bottomSheetStyle: {
      backgroundColor: color.BOTTOM_SHEET_BACKGROUND,
    },
    bottomSheetIndicatorStyle: {
      backgroundColor: color.MESSAGE_FLAG,
    },
    bottomSheetScrollView: {
      backgroundColor: color.BOTTOM_SHEET_BACKGROUND,
    },
  });

  return { styles, color };
};
