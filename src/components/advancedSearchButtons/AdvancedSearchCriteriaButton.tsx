import { type ViewStyle, Pressable, StyleSheet, View } from 'react-native';
import {
  BenefitIconSet,
  CustomText,
  FONT_SIZES,
  FONT_WEIGHTS,
  SPACING,
  type Theme,
  useThemeAwareObject,
} from 'b-ui-lib';
import { TEST_IDS } from '../../constants/testIds';

type Props = {
  isActive: boolean;
  handlePress?: () => void;
  criteriaCount?: string;
  style?: {
    container?: ViewStyle | ViewStyle[];
  };
};

export const AdvanceSearchCriteriaButton = ({
  isActive,
  handlePress,
  criteriaCount,
  style,
}: Props) => {
  const { styles, color } = useThemeAwareObject((theme) =>
    createStyles(theme, isActive)
  );

  return (
    <Pressable
      testID={TEST_IDS.advancedSearchCriteriaButton}
      style={[styles.container, style?.container]}
      onPress={handlePress}
    >
      <View style={styles.body}>
        {!isActive && (
          <BenefitIconSet
            name="filter-small"
            size={12}
            color={color.SEARCH_CRITERIA_ICON_COLOR}
            style={styles.filerIcon}
          />
        )}

        <CustomText style={styles.text}>Adv Criteria</CustomText>

        {criteriaCount && criteriaCount !== '0' && (
          <View style={styles.criteriaCountContainer}>
            <CustomText style={styles.criteriaTextCount}>
              {criteriaCount}
            </CustomText>
          </View>
        )}
      </View>
    </Pressable>
  );
};

const createStyles = ({ color }: Theme, isActive: boolean) => {
  const styles = StyleSheet.create({
    container: {},
    body: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 0.5,
      borderColor: color.SEARCH_CRITERIA_BORDER,
      borderRadius: SPACING.S,
      paddingHorizontal: SPACING.S,
      paddingVertical: SPACING.XS,
      backgroundColor: isActive
        ? color.SEARCH_CRITERIA_BACKGROUND_COLOR
        : undefined,
    },
    filerIcon: {
      marginRight: SPACING.XS,
    },
    text: {
      fontSize: FONT_SIZES.TWELVE,
      color: isActive ? color.WHITE : color.TEXT_SEARCH_CRITERIA,
    },
    criteriaCountContainer: {
      width: 20,
      height: 20,
      marginLeft: SPACING.S,
      backgroundColor: color.BLUE_DEFAULT,
      borderRadius: 10,
      justifyContent: 'center',
      alignItems: 'center',
    },
    criteriaTextCount: {
      fontSize: FONT_SIZES.TEN,
      fontWeight: FONT_WEIGHTS.BOLD,
      color: color.WHITE,
      textAlign: 'center',
    },
  });

  return { styles, color };
};
