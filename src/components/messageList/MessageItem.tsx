import { type FC } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import {
  useThemeAwareObject,
  type Message,
  type Theme,
  CustomText,
  FONT_SIZES,
  FONT_WEIGHTS,
  IconTextButton,
  MessageAvatar,
  SPACING,
  MessageIcons,
  MessageDate,
  Checkbox,
} from 'b-ui-lib';
import { TEST_IDS } from '../../constants/testIds';
import { SwipeableMessageItem } from './SwipeableMessageItem';

type Props = {
  message: Message;
  isMultiSelectActive?: boolean;
  isChecked: boolean;
  handleTapMessage: (messageId: string) => void;
  handleLongTapToSelectEmail: (messageId: string) => void;
  handleTapToSelectAdditionalEmail: (messageId: string) => void;
  handleDeselectMessage: (messageId: string) => void;
  handleFlagPress?: (messageId: string) => void;
  onReply?: (message: Message) => void;
  onDelete?: (message: Message) => void;
  testID?: string;
  messageSectionIndex?: number; // This is used only for test purposes
};

export const MessageItem: FC<Props> = ({
  message,
  isMultiSelectActive,
  isChecked,
  handleTapMessage,
  handleLongTapToSelectEmail,
  handleTapToSelectAdditionalEmail,
  handleDeselectMessage,
  handleFlagPress,
  onReply,
  onDelete,
  testID,
  messageSectionIndex,
}) => {
  const {
    sentDate,
    from,
    subject,
    body,
    avatarName,
    isFlagged,
    hasReplies,
    hasAttachments,
    hasComments,
    hasMetadata,
    hasFolders,
    hasCases,
    isViewed,
    inOut,
  } = message || {};
  const { styles, color } = useThemeAwareObject((theme) =>
    createStyles(theme, isChecked, isViewed)
  );

  const handleTapMessageCheckbox = (messageId: string) => {
    if (isChecked) {
      return handleDeselectMessage(messageId);
    }

    return handleTapToSelectAdditionalEmail(messageId);
  };

  return (
    <SwipeableMessageItem
      message={message}
      onReply={onReply}
      onDelete={onDelete}
      replyTestID={`${messageSectionIndex}-${TEST_IDS.messageListSwipeReplyAction}`}
      deleteTestID={`${messageSectionIndex}-${TEST_IDS.messageListSwipeDeleteAction}`}
    >
      <Pressable
        testID={testID}
        onPress={() => handleTapMessage(message.id)}
        onLongPress={() => handleLongTapToSelectEmail(message.id)}
        style={styles.container}
      >
        <MessageAvatar avatarName={avatarName} inOut={inOut} />

        <View style={styles.messageInfoContainer}>
          <CustomText
            testID={
              isViewed
                ? `${messageSectionIndex}-${TEST_IDS.messageListUsernameNotBoldText}`
                : `${messageSectionIndex}-${TEST_IDS.messageListUsernameBoldText}`
            }
            style={styles.fromText}
            numberOfLines={1}
          >
            {from}
          </CustomText>

          <CustomText
            testID={`${messageSectionIndex}-${TEST_IDS.messageListSubjectText}`}
            style={styles.subjectText}
            numberOfLines={1}
          >
            {subject}
          </CustomText>

          <CustomText
            testID={`${messageSectionIndex}-${TEST_IDS.messageListBodyText}`}
            style={styles.bodyText}
            numberOfLines={1}
          >
            {body}
          </CustomText>

          <MessageIcons
            iconSize={16}
            hasReplies={hasReplies}
            hasAttachments={hasAttachments}
            hasComments={hasComments}
            hasMetadata={hasMetadata}
            hasFolders={hasFolders}
            hasCases={hasCases}
            messageSectionIndex={messageSectionIndex}
          />
        </View>

        {!isMultiSelectActive ? (
          <View style={styles.dateContainer}>
            <MessageDate sentDate={sentDate} />

            <IconTextButton
              testID={
                isFlagged
                  ? `${messageSectionIndex}-${TEST_IDS.messageListFlagIcon}`
                  : `${messageSectionIndex}-${TEST_IDS.messageListUnFlagIcon}`
              }
              iconName={isFlagged ? 'flag-filled' : 'flag'}
              iconSize={20}
              iconColor={color.MESSAGE_FLAG}
              onPress={() => handleFlagPress && handleFlagPress(message.id)}
              containerStyle={styles.flag}
            />
          </View>
        ) : (
          <Checkbox
            testID={
              isChecked
                ? `${messageSectionIndex}-${TEST_IDS.messageListCheckedCheckbox}`
                : `${messageSectionIndex}-${TEST_IDS.messageListUnCheckedCheckbox}`
            }
            isChecked={isChecked}
            onPress={() => handleTapMessageCheckbox(message.id)}
          />
        )}
      </Pressable>
    </SwipeableMessageItem>
  );
};

const createStyles = (
  { color }: Theme,
  isChecked: boolean,
  isViewed: boolean
) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: isChecked
        ? color.MESSAGE_ITEM__SELECTED_BACKGROUND
        : color.MESSAGE_ITEM__BACKGROUND,
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 16,
      padding: SPACING.M,
      borderTopWidth: 1,
      borderTopColor: color.PRESSABLE_HOVER,
    },
    messageInfoContainer: {
      flex: 1,
      gap: SPACING.SIX,
    },
    dateContainer: {
      justifyContent: 'space-between',
      alignItems: 'flex-end',
    },
    flag: {
      padding: 0,
      alignSelf: 'flex-end',
    },
    fromText: {
      fontSize: FONT_SIZES.FOURTEEN,
      lineHeight: FONT_SIZES.FOURTEEN,
      fontWeight: !isViewed ? FONT_WEIGHTS.BOLD : FONT_WEIGHTS.NORMAL,
      color: color.TEXT_DEFAULT,
    },
    subjectText: {
      fontSize: FONT_SIZES.TWELVE,
      lineHeight: FONT_SIZES.TWELVE,
      fontWeight: !isViewed ? FONT_WEIGHTS.BOLD : FONT_WEIGHTS.NORMAL,
      color: color.MESSAGE_ITEM__SUBJECT,
    },
    bodyText: {
      fontSize: FONT_SIZES.TWELVE,
      lineHeight: FONT_SIZES.TWELVE,
      color: color.MESSAGE_ITEM__DESC,
    },
  });

  return { styles, color };
};
