import { type FC, type ReactNode } from 'react';
import { StyleSheet, View } from 'react-native';
import { GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { Gesture } from 'react-native-gesture-handler';
import {
  type Theme,
  useThemeAwareObject,
  type Message,
} from 'b-ui-lib';

const SWIPE_THRESHOLD = 80;
const ACTION_WIDTH = 80;

type Props = {
  message: Message;
  onLeftSwipe?: (message: Message) => void;
  onRightSwipe?: (message: Message) => void;
  leftAction?: ReactNode;
  rightAction?: ReactNode;
  children: ReactNode;
  leftActionTestID?: string;
  rightActionTestID?: string;
};

export const SwipeableMessageItem: FC<Props> = ({
  message,
  onLeftSwipe,
  onRightSwipe,
  leftAction,
  rightAction,
  children,
  leftActionTestID,
  rightActionTestID,
}) => {
  const { styles } = useThemeAwareObject(createStyles);
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(0);

  const handleLeftSwipe = () => {
    onLeftSwipe?.(message);
  };

  const handleRightSwipe = () => {
    onRightSwipe?.(message);
  };

  const panGesture = Gesture.Pan()
    .activeOffsetX([-10, 10]) // Only activate when horizontal movement exceeds 10px
    .failOffsetY([-10, 10]) // Fail if vertical movement exceeds 10px
    .simultaneousWithExternalGesture() // Allow other gestures (like scroll) to work
    .onStart(() => {
      opacity.value = withTiming(1, { duration: 200 });
    })
    .onUpdate((event) => {
      const { translationX } = event;

      // Limit swipe range
      if (translationX > 0) {
        // Swipe right (reply) - limit to ACTION_WIDTH
        translateX.value = Math.min(translationX, ACTION_WIDTH);
      } else {
        // Swipe left (delete) - limit to -ACTION_WIDTH
        translateX.value = Math.max(translationX, -ACTION_WIDTH);
      }
    })
    .onEnd((event) => {
      const { translationX, velocityX } = event;

      // Determine if threshold was met
      const shouldTriggerAction =
        Math.abs(translationX) > SWIPE_THRESHOLD || Math.abs(velocityX) > 500;

      if (shouldTriggerAction) {
        if (translationX > 0 && onRightSwipe) {
          // Swipe right
          runOnJS(handleRightSwipe)();
        } else if (translationX < 0 && onLeftSwipe) {
          // Swipe left
          runOnJS(handleLeftSwipe)();
        }
      }

      // Reset position
      translateX.value = withSpring(0, { damping: 20, stiffness: 300 });
      opacity.value = withTiming(0, { duration: 200 });
    });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const rightActionStyle = useAnimatedStyle(() => ({
    opacity: translateX.value > 0 ? opacity.value : 0,
    transform: [
      {
        scale:
          translateX.value > 0
            ? Math.min(translateX.value / ACTION_WIDTH, 1)
            : 0,
      },
    ],
  }));

  const leftActionStyle = useAnimatedStyle(() => ({
    opacity: translateX.value < 0 ? opacity.value : 0,
    transform: [
      {
        scale:
          translateX.value < 0
            ? Math.min(Math.abs(translateX.value) / ACTION_WIDTH, 1)
            : 0,
      },
    ],
  }));

  // Only render swipe functionality if at least one action is provided
  if (!onRightSwipe && !onLeftSwipe) {
    return <>{children}</>;
  }

  return (
    <View style={styles.container}>
      {/* Right Action (Left side, shown on right swipe) */}
      {onRightSwipe && rightAction && (
        <Animated.View
          style={[styles.rightAction, rightActionStyle]}
          testID={rightActionTestID}
        >
          {rightAction}
        </Animated.View>
      )}

      {/* Left Action (Right side, shown on left swipe) */}
      {onLeftSwipe && leftAction && (
        <Animated.View
          style={[styles.leftAction, leftActionStyle]}
          testID={leftActionTestID}
        >
          {leftAction}
        </Animated.View>
      )}

      {/* Main Content */}
      <GestureDetector gesture={panGesture}>
        <Animated.View style={[styles.mainContent, animatedStyle]}>
          {children}
        </Animated.View>
      </GestureDetector>
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      position: 'relative',
    },
    rightAction: {
      position: 'absolute',
      left: 0,
      top: 0,
      bottom: 0,
      width: ACTION_WIDTH,
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1,
    },
    leftAction: {
      position: 'absolute',
      right: 0,
      top: 0,
      bottom: 0,
      width: ACTION_WIDTH,
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1,
    },
    mainContent: {
      zIndex: 2,
      backgroundColor: color.BACKGROUND || '#FFFFFF',
    },
  });

  return { styles };
};
