import { type FC, type ReactNode } from 'react';
import { StyleSheet, View } from 'react-native';
import {
  GestureDetector,
} from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { Gesture } from 'react-native-gesture-handler';
import {
  BenefitIconSet,
  type Theme,
  useThemeAwareObject,
  type Message,
} from 'b-ui-lib';

const SWIPE_THRESHOLD = 80;
const ACTION_WIDTH = 80;

type Props = {
  message: Message;
  onReply?: (message: Message) => void;
  onDelete?: (message: Message) => void;
  children: ReactNode;
  replyTestID?: string;
  deleteTestID?: string;
};

export const SwipeableMessageItem: FC<Props> = ({
  message,
  onReply,
  onDelete,
  children,
  replyTestID,
  deleteTestID,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(0);

  const handleReply = () => {
    onReply?.(message);
  };

  const handleDelete = () => {
    onDelete?.(message);
  };

  const panGesture = Gesture.Pan()
    .onStart(() => {
      opacity.value = withTiming(1, { duration: 200 });
    })
    .onUpdate((event) => {
      const { translationX } = event;

      // Limit swipe range
      if (translationX > 0) {
        // Swipe right (reply) - limit to ACTION_WIDTH
        translateX.value = Math.min(translationX, ACTION_WIDTH);
      } else {
        // Swipe left (delete) - limit to -ACTION_WIDTH
        translateX.value = Math.max(translationX, -ACTION_WIDTH);
      }
    })
    .onEnd((event) => {
      const { translationX, velocityX } = event;

      // Determine if threshold was met
      const shouldTriggerAction = Math.abs(translationX) > SWIPE_THRESHOLD || Math.abs(velocityX) > 500;

      if (shouldTriggerAction) {
        if (translationX > 0 && onReply) {
          // Swipe right - reply
          runOnJS(handleReply)();
        } else if (translationX < 0 && onDelete) {
          // Swipe left - delete
          runOnJS(handleDelete)();
        }
      }

      // Reset position
      translateX.value = withSpring(0, { damping: 20, stiffness: 300 });
      opacity.value = withTiming(0, { duration: 200 });
    });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const replyActionStyle = useAnimatedStyle(() => ({
    opacity: translateX.value > 0 ? opacity.value : 0,
    transform: [
      {
        scale: translateX.value > 0 ? Math.min(translateX.value / ACTION_WIDTH, 1) : 0,
      },
    ],
  }));

  const deleteActionStyle = useAnimatedStyle(() => ({
    opacity: translateX.value < 0 ? opacity.value : 0,
    transform: [
      {
        scale: translateX.value < 0 ? Math.min(Math.abs(translateX.value) / ACTION_WIDTH, 1) : 0,
      },
    ],
  }));

  // Only render swipe functionality if at least one action is provided
  if (!onReply && !onDelete) {
    return <>{children}</>;
  }

  return (
    <View style={styles.container}>
      {/* Reply Action (Left side, shown on right swipe) */}
      {onReply && (
        <Animated.View style={[styles.replyAction, replyActionStyle]} testID={replyTestID}>
          <BenefitIconSet
            name="arrow-undo-outline"
            size={24}
            color={color.WHITE}
          />
        </Animated.View>
      )}

      {/* Delete Action (Right side, shown on left swipe) */}
      {onDelete && (
        <Animated.View style={[styles.deleteAction, deleteActionStyle]} testID={deleteTestID}>
          <BenefitIconSet
            name="x"
            size={24}
            color={color.WHITE}
          />
        </Animated.View>
      )}

      {/* Main Content */}
      <GestureDetector gesture={panGesture}>
        <Animated.View style={animatedStyle}>
          {children}
        </Animated.View>
      </GestureDetector>
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      position: 'relative',
    },
    replyAction: {
      position: 'absolute',
      left: 0,
      top: 0,
      bottom: 0,
      width: ACTION_WIDTH,
      backgroundColor: color.SUCCESS || '#4CAF50',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1,
    },
    deleteAction: {
      position: 'absolute',
      right: 0,
      top: 0,
      bottom: 0,
      width: ACTION_WIDTH,
      backgroundColor: color.ERROR || '#F44336',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1,
    },
  });

  return { styles, color };
};
