import React, { type RefObject, useCallback, useMemo, useState } from 'react';
import {
  type TextInputProps,
  type ViewStyle,
  StyleSheet,
  TextInput,
  View,
  Keyboard,
} from 'react-native';
import {
  type Theme,
  BenefitIconSet,
  FONT_SIZES,
  SPACING,
  useThemeAwareObject,
  IconButton,
  SuggestionItem,
} from 'b-ui-lib';
import _lodash from 'lodash';

export type SearchInputProps = TextInputProps & {
  searchInputRef?: RefObject<TextInput>;
  value: string;
  placeholder?: string;
  onChangeText: (text: string) => void;
  handleDebounceFunction?: (text: string, otherDebounceParams?: {}) => void;
  debounceWait?: number;
  handleInputClear: () => void;
  isSuggestionModalVisible?: boolean;
  suggestions?: string[];
  handleSuggestionPress?: (suggestion: string) => void;
  containerStyle?: ViewStyle;
  testID?: string;
  // We get debounceParameters to ensure the debounce function receives the latest values.
  // Otherwise, because of useCallback, it would only capture the initial values.
  debounceParameters?: {};
};

export const SearchInput: React.FC<SearchInputProps> = ({
  searchInputRef,
  value,
  placeholder,
  onChangeText,
  handleDebounceFunction,
  debounceWait,
  handleInputClear,
  suggestions,
  handleSuggestionPress,
  containerStyle,
  testID,
  debounceParameters,
  ...restProps
}) => {
  const [isInboxSearchInputFocused, setIsInboxSearchInputFocused] =
    useState(false);
  const { styles, color } = useThemeAwareObject(createStyles);

  const isSuggestionModalVisible = useMemo(
    () =>
      isInboxSearchInputFocused &&
      value &&
      value !== '' &&
      suggestions &&
      suggestions?.length > 0,
    [isInboxSearchInputFocused, value, suggestions]
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceFunction = useCallback(
    handleDebounceFunction
      ? _lodash.debounce(
          (latestSearchText: string, otherDebounceParams?: {}) => {
            handleDebounceFunction(latestSearchText, otherDebounceParams);
          },
          debounceWait ? debounceWait : 500
        )
      : () => {},
    []
  );

  const handleTextChange = (inputValue: string) => {
    onChangeText(inputValue);

    handleDebounceFunction && debounceFunction(inputValue, debounceParameters);
  };

  const clearInput = () => {
    handleInputClear();
  };

  const onSuggestionPress = (suggestion: string) => {
    handleSuggestionPress && handleSuggestionPress(suggestion);
    Keyboard.dismiss();
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.inputContainer}>
        <View style={styles.icon}>
          <BenefitIconSet
            name={'search'}
            size={20}
            color={color.SEARCH_FIELD_CALENDAR_ICON}
          />
        </View>
        <TextInput
          testID={testID}
          ref={searchInputRef}
          {...restProps}
          onFocus={() => setIsInboxSearchInputFocused(true)}
          onBlur={() => setIsInboxSearchInputFocused(false)}
          style={styles.input}
          value={value}
          onChangeText={handleTextChange}
          autoCapitalize="none"
          placeholder={placeholder}
          placeholderTextColor={color.SEARCH_FIELD_CALENDAR_ICON}
        />

        {value && (
          <IconButton
            name="x-circle-filled"
            size={20}
            color={color.SEARCH_FIELD_CALENDAR_ICON}
            onPress={clearInput}
            containerStyle={styles.icon}
          />
        )}
      </View>

      {isSuggestionModalVisible && (
        <View style={styles.dropdown}>
          {suggestions?.map((filteredSuggestion, index) => (
            <SuggestionItem
              key={index}
              suggestion={filteredSuggestion}
              handleSuggestionPress={onSuggestionPress}
              testStyles={styles.suggestionItemStyles}
            />
          ))}
        </View>
      )}
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {},
    inputContainer: {
      backgroundColor: color.TAB_BACKGROUND_ACTIVE,
      justifyContent: 'space-between',
      flexDirection: 'row',
      gap: SPACING.TEN,
      paddingVertical: SPACING.S,
      paddingHorizontal: SPACING.M,
      borderRadius: SPACING.TEN,
    },
    icon: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    input: {
      flex: 1,
      margin: 0,
      padding: 0,
      fontSize: FONT_SIZES.FOURTEEN,
      lineHeight: FONT_SIZES.FOURTEEN,
      color: color.TEXT_DEFAULT,
    },
    dropdown: {
      position: 'absolute',
      maxHeight: 280,
      top: '100%',
      left: 0,
      right: 0,
      marginTop: SPACING.S,
      zIndex: 9999,
      borderRadius: SPACING.TEN,
      backgroundColor: color.TAB_BACKGROUND_ACTIVE,
      elevation: 3,
      shadowColor: color.SEARCH_CRITERIA_ICON_COLOR,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      paddingBottom: SPACING.SIX,
    },
    suggestionItemStyles: {
      borderBottomWidth: 1,
      borderBottomColor: color.SEARCH_FIELD_CALENDAR_ICON,
    },
  });

  return { styles, color };
};
