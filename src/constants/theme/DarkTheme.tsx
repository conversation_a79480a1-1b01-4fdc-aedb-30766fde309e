import type { ColorTheme, Theme } from './ThemeInterface';

const DARK_COLOR_THEME: ColorTheme = {
  WHITE: 'rgba(255,255,255,1)',
  BLACK: 'rgba(0,0,0,1)',
  GREY1: 'rgba(215,215,219,1)',
  GREY2: 'rgba(201,201,207,1)',
  GREY3: 'rgba(97,97,107,1)',
  GREY_OPACITY: 'rgba(97,97,107,0.5)',
  BLACK_TRANS: 'rgba(0,0,0,0.5)',
  HEADER_COLOR: 'rgba(48,48,54,1)',
  GREY6: '#E5E5E5',
  BLUE_SEA: 'rgba(102,140,255,1)',
  PIN_ACTIVE: 'rgba(255,162,0,1)',
  ORANGE_BG: 'rgba(255,210,178,1)',
  COMPANY_BACKGROUND: '#3C64DD4D',
  BORDER_COLOR: '#494950CC',
  COMPANY_STAT_TAB: '#FFFFFF1A',
  LIGHTGRAY: '#F6F6F7',
  BACKGROUND: '#303036',
  PRESSABLE: '#94949E',
  CHIP_GRAY: '#6C6C7A',
  PRIMARY_DIMMED: '#94949E',
  TEXT_DEFAULT: '#FFFFFF',
  HALF_DIMMED: '#E4E4E7',
  TEXT_DIMMED: '#94949E',
  TEXT_DISABLED: '#494950',
  BORDER_COLOR_FORM: '#494950CC',
  ORANGE: '#B24A00',
  BROWN: '#FFE1BB',
  RANK_BACKGROUND: '#3956AC',
  BRAND_BLUE: '#F2F2F3',
  BACKDROP_COLOR: '#303036',
  PRESSABLE_HOVER: '#6C6C7A',
  CHIP_GREEN: '#3C6600',
  TAB_BACKGROUND: '#303036',
  TAB_BACKGROUND_ACTIVE: '#494950',
  RED: '#F2F2F3',
  PINK: '#990021',
  ERROR: '#B24A00',
  DESTRUCTIVE_DEFAULT: '#FF6686',
  INVERTED_DIMMED: '#494950',
  PRIMARY_HOVER: '#61616B',
  INVERTED_HALF_DIMMED: '#D7D7DB',
  SUCCESS: '#77CC00',
  HIGHLIGHT: '#0286FF1A',

  // Map Colors
  GEOMETRY: '#212121',
  LABELS_TEXT_FILL: '#757575',
  LABELS_TEXT_STROKE: '#212121',
  ADMINISTRATIVE_GEOMETRY: '#757575',
  ADMINISTRATIVE_COUNTRY_TEXT_FILL: '#9e9e9e',
  ADMINISTRATIVE_LOCALITY_TEXT_FILL: '#bdbdbd',
  POI_TEXT_FILL: '#757575',
  POI_PARK_GEOMETRY: '#181818',
  POI_PARK_TEXT_FILL: '#616161',
  POI_PARK_TEXT_STROKE: '#1b1b1b',
  GEOMETRY_FILL: '#2c2c2c',
  ROAD_TEXT_FILL: '#8a8a8a',
  ROAD_ARTERIAL_GEOMETRY: '#373737',
  ROAD_HIGHWAY_GEOMETRY: '#4e4e4e',
  ROAD_LOCAL_TEXT_FILL: '#616161',
  TRANSIT_TEXT_FILL: '#757575',
  WATER_GEOMETRY: '#000000',
  WATER_TEXT_FILL: '#3d3d3d',

  BRAND_DEFAULT: '#FFB533',
  BOTTOM_TAB_ACTIVE_COLOR_BSUITE: '#F9F9FA',
  OUTBOUND_EMAIL_ARROW_ICON: '#FFA200',
  GREY_ARROW_ICON: '#21272A',
  CHECK_BLUE: '#006FFD',
  EMAIL_ICON: '#C9C9CF',
  BORDER_EMAIL_CATEGORY: '#E9E9E9',
  TEXT_GREY: '#999999',
  TEXT_GREY_DARKER: '#F2F2F3',
  TEXT_GREY_MORE_DARKER: '#303036',
  AVATAR_BACKGROUND: '#FF8833',
  ARROW_EXPAND: '#94949E',
  PURPLE: '#FA23F5',
  ORANGE_BRIEFCASE: '#FFC499',
  MESSAGE_ITEM_AVATAR_PURPLE: '#381E72',
  MESSAGE_ITEM__SUBJECT: '#D7D7DB',
  MESSAGE_ITEM__DESC: '#D7D7DB',
  MESSAGE_ITEM__BACKGROUND: '#494950',
  MESSAGE_ITEM__SELECTED_BACKGROUND: '#797986',
  MESSAGE_ITEM__ARROW_DOWN: '#88CC29',
  MESSAGE_FLAG: '#FFA200',
  MESSAGE_BUTTONS_BACKGROUND: '#303036',
  MESSAGE_CATEGORY_ATTACHMENTS: '#F2F2F3',
  MESSAGE_CATEGORY_ATTACHMENTS_BUTTON: '#FFA200',
  MODAL_BACKGROUND_COLOR: '#303036',
  MODAL_RECIPIENT_BACKGROUND: '#FFFFFF',
  MODAL_RECIPIENT_TEXT: '#18181B',
  EMAIL_COLOR: '#3355FF',
  BORDER_EMAIL_FOLDER: '#61616B',
  TEXT_DISABLED_2: '#C9C9CF',
  TEXT_DEFAULT_INVETRED: '#AFAFB6',
  TEXT_DEFAULT_ORANGE: '#FFA200',
  TEXT_SEARCH_CRITERIA: '#C9C9CF',
  SEARCH_CRITERIA_BORDER: '#C5C6CC',
  SEARCH_CRITERIA_BACKGROUND_COLOR: '#494950',
  SEARCH_CRITERIA_ICON_COLOR: '#8F9098',
  BLUE_DEFAULT: '#FFB533',
  SKELETON_BACKGROUND: '#6C6C7A',
  SKELETON_FOREGROUND: '#A9A9B3',
  TEXT_SEARCH_INVERTED: '#F2F2F3',
  SEARCH_FIELD_BORDER: '#61616B',
  SEARCH_FIELD_PLACEHOLDER: '#797986',
  SEARCH_FIELD_CALENDAR_ICON: '#AFAFB6',
  KEYWORD_SUGGESTION_COLOR: '#FFB533',
  KEYWORD_SUGGESTION_BACKGROUND_COLOR: '#494950',
  MESSAGE_ITEM_ICON: '#D7D7DB',
  NOTIFICATION_PAPERCLIP_TEXT: '#494950',
  NOTIFICATION_FOLDER_ICON: '#FFFFFF',
  NOTIFICATION_USERNAME: '#FFB533',
  NOTIFICATION_BODY: '#D7D7DB',
  NOTIFICATION_TITLE_BLUR: '#F2F2F3',
  NOTIFICATION_BACK_BUTTON: '#FFA200',
  NOTIFICATION_STAR_ICON: '#FFA200',
  NOTIFICATION_TITLE: '#F2F2F3',
  NOTIFICATION_ICON: '#FFA200',
  NOTIFICATION_DATETIME: '#E4E4E7',
  NOTIFICATION_BUTTON_BACKGROUND: '#FFB533',
  NOTIFICATION_BUTTON_TEXT: '#FFFFFF',
  NOTIFICATION_BOTTOM_SHEET_TEXT_COLOR: '#FFFFFF',
  NOTIFICATION_BOTTOM_SHEET_TEXT_BACKGROUND_COLOR: '#FFB533',
  NOTIFICATION_NOT_VIEWED_BODY: '#D7D7DB',
  BOTTOM_SHEET_BACKGROUND: '#303036',
  TASK_DETAILS_BORDER: '#61616B',
  TASK_DETAILS_USERNAME: '#FFB533',
  TASK_CLOSED: '#FF335F',
  TASK_NOT_ACTIVATED: '#AFAFB6',
  DOCUMENT_COMPLETED: '#70BF00',
  DOCUMENT_REQUESTED: '#F83',
  DOCUMENT_ARROW_ICON: '#FFFFFF',
  CONTACTS_FILTER_BACKGROUND: '#61616B',
  CONTACTS_SELECTED_FILTER_BACKGROUND: '#494950',
  CONTACTS_FILTER_ICON: '#AFAFB6',
  CONTACTS_SELECTED_FILTER_ICON: '#FFB533',
  CONTACTS_ITEM_SUBTITLE: '#999999',
};

export const DARK_THEME_IMAGES = {
  CARGO_SHIP: require('./images/cargo-ship-light.png'),
  MESSAGE: require('./images/message.png'),
  CALENDAR: require('./images/calendar.png'),
  ALERT: require('./images/alertTriangle.png'),
  ANCHOR: require('./images/anchor-dark.png'),
  CHEVRON_RIGHT: require('./images/chevronRight-dark.png'),
  SEARCH: require('./images/search-dark.png'),
  USER: require('./images/user-dark.png'),
  PENCIL: require('./images/pencil.png'),
  CHEVRON_UP: require('./images/chevronUp.png'),
  ALLOTTEES: require('./images/home.png'),
  FAMILY: require('./images/family.png'),
  GENERAL: require('./images/generalInfo.png'),
  GREEK: require('./images/details.png'),
  PROPELLER: require('./images/propeller-dark.png'),
  CHARACTERISTICS: require('./images/characteristics.png'),
  DOCUMENTS: require('./images/calendar.png'),
  NOTIFICATIONS: require('./images/notifications.png'),
  UNREAD: require('./images/unread.png'),
  MESSAGES: require('./images/messageHome.png'),
  SERVICE_PHOTO: require('./images/cardServicePhoto.png'),
  WARNING_GREY: require('./images/warning_grey.png'),
  AT_SEA: require('./images/atSea.png'),
  DOWN: require('./images/ChevronDown.png'),
  UP: require('./images/chevronUp.png'),
  DOWNLOAD: require('./images/download.png'),
  RENEWAL: require('./images/renewal.png'),
  INFO: require('./images/info.png'),
  SAVE: require('./images/save.png'),
  ARROW_RIGHT: require('./images/arrowRight.png'),
  AT_PORT: require('./images/atPort.png'),
  VESSEL_PLANS: require('./images/vesselPlans.png'),
  CREW_PHOTO: require('./images/crewPhoto.png'),
  OK: require('./images/ok.png'),
  ATTACHMENT: require('./images/attachment.png'),
  CALENDAR_GREY: require('./images/calendarGrey.png'),
  LOCATION: require('./images/location.png'),
  EYE: require('./images/eye.png'),
  SEA: require('./images/sea.png'),
  APPRAISALS: require('./images/appraisals.png'),
  FILES: require('./images/files.png'),
  MEDICAL: require('./images/medical.png'),
  PROFILE: require('./images/profile.png'),
  DOCUMENT: require('./images/training.png'),
  WHEEL: require('./images/wheel-dark.png'),
  MAP_PIN: require('./images/map-pin-dark.png'),
  BENEFIT_LOGO: require('./images/benefit.png'),
  BACK: require('./images/BackButton.svg'),
  CLIPBOARD: require('./images/clipboard.png'),
  EXTERNALLINK: require('./images/external-link.png'),
  MENUDOTS: require('./images/menuDots.png'),
  MAP_PIN_TRIP_XXXL: require('./images/mapPinTripXXXL.png'),
  MAP_PIN_TRIP: require('./images/mapPinTrip.png'),
  MAP_PIN_PORT: require('./images/mapPinPort.png'),
  ROUTE: require('./images/route.png'),
  SMALL_ARROW: require('./images/arrowLeft.png'),
  VESSEL_PLACEHOLDER: require('./images/vesselPlaceholder.png'),
  SEAMAN_PLACEHOLDER: require('./images/seamanPlaceholderSquare.png'),
  COMMENTS: require('./images/comments.png'),
  ADD_COMMENT: require('./images/plus.png'),
  FILTER: require('./images/filter.png'),
  STAR: require('./images/star.png'),
  ENVELOPE: require('./images/envelope.png'),
  ENVELOPE_ACTIVE: require('./images/envelope-active.png'),
  LINK: require('./images/link.png'),
  USERS: require('./images/users.png'),
  REPLY: require('./images/reply.png'),
  YELLOW_ARROW: require('./images/yellowArrow.png'),
  BA_LOGO: require('./images/ba_logo.png'),
  AUDIO_VISUALIZATION: require('./images/audio-visualization.gif'),
  CAMERA: require('./images/camera.png'),
  MICROPHONE: require('./images/microphone.png'),
  B_AUDIT_LOGO: require('./images/bAuditLogo-dark.png'),
  B_IN_CHARGE_LOGO: require('./images/bInCharge-dark.png'),
  B_TEAM_LOGO: require('./images/bTeamLogoDarkTheme.png'),
  B_SIGNATURE_LOGO: require('./images/bSignatureLogoDarkTheme.png'),
};

export const DARK_THEME_ID = 'dark';

export const DARK_THEME: Theme = {
  id: DARK_THEME_ID,
  color: DARK_COLOR_THEME,
  images: DARK_THEME_IMAGES,
};
