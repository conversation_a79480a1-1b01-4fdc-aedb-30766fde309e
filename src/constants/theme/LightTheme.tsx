import type { ColorTheme, Theme } from './ThemeInterface';

const LIGHT_COLOR_THEME: ColorTheme = {
  WHITE: 'rgba(255,255,255,1)',
  BLACK: 'rgba(0,0,0,1)',
  GREY1: 'rgba(215,215,219,1)',
  GREY2: 'rgba(201,201,207,1)',
  GREY3: 'rgba(97,97,107,1)',
  GREY_OPACITY: 'rgba(97,97,107,0.5)',
  BLACK_TRANS: 'rgba(0,0,0,0.5)',
  HEADER_COLOR: 'rgba(48,48,54,1)',
  GREY6: '#E5E5E5',
  BLUE_SEA: 'rgba(102,140,255,1)',
  PIN_ACTIVE: 'rgba(255,162,0,1)',
  ORANGE_BG: 'rgba(255,210,178,1)',
  COMPANY_BACKGROUND: '#D1DBFA33',
  BORDER_COLOR: '#D7D7DB',
  COMPANY_STAT_TAB: '#FFFFFF4D',
  LIGHTGRAY: '#F6F6F7',
  BACKGROUND: '#f2f2f3',
  PRESSABLE: '#fff',
  CHIP_GRAY: '#f2f2f3',
  PRIMARY_DIMMED: '#F9F9FA',
  TEXT_DEFAULT: '#18181B',
  HALF_DIMMED: '#494950',
  TEXT_DIMMED: '#797986',
  TEXT_DISABLED: '#C9C9CF',
  BORDER_COLOR_FORM: '#F2F2F3',
  ORANGE: '#FFE1CC',
  BROWN: '#662B00',
  RANK_BACKGROUND: '#D1DBFA',
  BRAND_BLUE: '#3355FF',
  BACKDROP_COLOR: '#797986',
  PRESSABLE_HOVER: '#E4E4E7',
  CHIP_GREEN: '#E9FAD1',
  TAB_BACKGROUND: '#F9F9FA',
  TAB_BACKGROUND_ACTIVE: '#FFFFFF',
  RED: '#990021',
  PINK: '#FFCCD7',
  ERROR: '#990021',
  DESTRUCTIVE_DEFAULT: '#FF335F',
  INVERTED_DIMMED: '#AFAFB6',
  PRIMARY_HOVER: '#61616B',
  INVERTED_HALF_DIMMED: '#D7D7DB',
  SUCCESS: '#77CC00',
  HIGHLIGHT: '#0286FF1A',
  BOTTOM_TAB_ACTIVE_COLOR_BSUITE: '#88CC29',
  OUTBOUND_EMAIL_ARROW_ICON: '#FFA200',
  GREY_ARROW_ICON: '#21272A',
  CHECK_BLUE: '#006FFD',
  EMAIL_ICON: '#797986',
  BORDER_EMAIL_CATEGORY: '#E9E9E9',
  TEXT_GREY: '#999999',
  TEXT_GREY_DARKER: '#3F3F3F',
  TEXT_GREY_MORE_DARKER: '#303036',
  AVATAR_BACKGROUND: '#FF8833',
  ARROW_EXPAND: '#94949E',
  PURPLE: '#FA23F5',
  ORANGE_BRIEFCASE: '#FFC499',
  MESSAGE_ITEM_AVATAR_PURPLE: '#FFFFFF',
  MESSAGE_ITEM__SUBJECT: '#494950',
  MESSAGE_ITEM__DESC: '#797986',
  MESSAGE_ITEM__BACKGROUND: '#FFFFFF',
  MESSAGE_ITEM__SELECTED_BACKGROUND: '#D6E7FF',
  MESSAGE_ITEM__ARROW_DOWN: '#88CC29',
  MESSAGE_FLAG: '#3355FF',
  MESSAGE_BUTTONS_BACKGROUND: '#FFFFFF',
  MESSAGE_CATEGORY_ATTACHMENTS: '#797986',
  MESSAGE_CATEGORY_ATTACHMENTS_BUTTON: '#FFFFFF',
  MODAL_BACKGROUND_COLOR: '#F2F2F3',
  MODAL_RECIPIENT_BACKGROUND: '#FFFFFF',
  MODAL_RECIPIENT_TEXT: '#18181B',
  EMAIL_COLOR: '#3355FF',
  BORDER_EMAIL_FOLDER: '#E9E9E9',
  TEXT_DISABLED_2: '#797986',
  TEXT_DEFAULT_INVETRED: '#18181B',
  TEXT_DEFAULT_ORANGE: '#18181B',
  TEXT_SEARCH_CRITERIA: '#1F2024',
  SEARCH_CRITERIA_BORDER: '#C5C6CC',
  SEARCH_CRITERIA_BACKGROUND_COLOR: '#006FFD',
  SEARCH_CRITERIA_ICON_COLOR: '#8F9098',
  BLUE_DEFAULT: '#006FFD',
  SKELETON_BACKGROUND: '#E0E0E0',
  SKELETON_FOREGROUND: '#F5F5F5',
  TEXT_SEARCH_INVERTED: '#18181B',
  SEARCH_FIELD_BORDER: '#F2F2F3',
  SEARCH_FIELD_PLACEHOLDER: '#797986',
  SEARCH_FIELD_CALENDAR_ICON: '#AFAFB6',
  KEYWORD_SUGGESTION_COLOR: '#494950',
  KEYWORD_SUGGESTION_BACKGROUND_COLOR: '#F2F2F3',
  MESSAGE_ITEM_ICON: '#79747E',
  NOTIFICATION_PAPERCLIP_TEXT: '#494950',
  NOTIFICATION_FOLDER_ICON: '#303036',
  NOTIFICATION_USERNAME: '#797986',
  NOTIFICATION_BODY: '#D7D7DB',
  NOTIFICATION_TITLE_BLUR: '#61616B',
  NOTIFICATION_BACK_BUTTON: '#494950',
  NOTIFICATION_STAR_ICON: '#797986',
  NOTIFICATION_TITLE: '#494950',
  NOTIFICATION_ICON: '#FFA200',
  NOTIFICATION_DATETIME: '#999999',
  NOTIFICATION_BUTTON_BACKGROUND: '#F9F9FA',
  NOTIFICATION_BUTTON_TEXT: '#3355ff',
  NOTIFICATION_BOTTOM_SHEET_TEXT_COLOR: '#797986',
  NOTIFICATION_BOTTOM_SHEET_TEXT_BACKGROUND_COLOR: '#F9F9FA',
  NOTIFICATION_NOT_VIEWED_BODY: '#18181B',
  BOTTOM_SHEET_BACKGROUND: '#F9F9FA',
  TASK_DETAILS_BORDER: '#E4E4E7',
  TASK_DETAILS_USERNAME: 'rgba(48, 48, 54, 0.90)',
  TASK_CLOSED: '#FF335F',
  TASK_NOT_ACTIVATED: '#AFAFB6',
  DOCUMENT_ARROW_ICON: '#bdbdbd',
  CONTACTS_FILTER_BACKGROUND: '#FFFFFF',
  CONTACTS_SELECTED_FILTER_BACKGROUND: '#D6E7FF',
  CONTACTS_FILTER_ICON: '#494950',
  CONTACTS_SELECTED_FILTER_ICON: '#3355FF',
  CONTACTS_ITEM_SUBTITLE: '#797986',

  // Map Colors
  GEOMETRY: '#f5f5f5',
  LABELS_TEXT_FILL: '#616161',
  LABELS_TEXT_STROKE: '#f5f5f5',
  ADMINISTRATIVE_GEOMETRY: '#616161',
  ADMINISTRATIVE_COUNTRY_TEXT_FILL: '#424242',
  ADMINISTRATIVE_LOCALITY_TEXT_FILL: '#616161',
  POI_TEXT_FILL: '#616161',
  POI_PARK_GEOMETRY: '#bdbdbd',
  POI_PARK_TEXT_FILL: '#757575',
  POI_PARK_TEXT_STROKE: '#f5f5f5',
  GEOMETRY_FILL: '#e0e0e0',
  ROAD_TEXT_FILL: '#757575',
  ROAD_ARTERIAL_GEOMETRY: '#dcdcdc',
  ROAD_HIGHWAY_GEOMETRY: '#c4c4c4',
  ROAD_LOCAL_TEXT_FILL: '#616161',
  TRANSIT_TEXT_FILL: '#616161',
  WATER_GEOMETRY: '#bdbdbd',
  WATER_TEXT_FILL: '#757575',
  BRAND_DEFAULT: '#FFB533',
  DOCUMENT_COMPLETED: '#70BF00',
  DOCUMENT_REQUESTED: '#F83',
};

export const LIGHT_THEME_IMAGES = {
  CARGO_SHIP: require('./images/cargo-ship-dark.png'),
  MESSAGE: require('./images/message.png'),
  CALENDAR: require('./images/calendar.png'),
  ALERT: require('./images/alertTriangle.png'),
  ANCHOR: require('./images/anchor-light.png'),
  CHEVRON_RIGHT: require('./images/chevronRight.png'),
  SEARCH: require('./images/search.png'),
  USER: require('./images/user-light.png'),
  PENCIL: require('./images/pencil.png'),
  CHEVRON_UP: require('./images/chevronUp.png'),
  ALLOTTEES: require('./images/home.png'),
  FAMILY: require('./images/family.png'),
  GENERAL: require('./images/generalInfo.png'),
  GREEK: require('./images/details.png'),
  PROPELLER: require('./images/propeller-light.png'),
  CHARACTERISTICS: require('./images/characteristics.png'),
  DOCUMENTS: require('./images/calendar.png'),
  NOTIFICATIONS: require('./images/notifications.png'),
  UNREAD: require('./images/unread.png'),
  MESSAGES: require('./images/messageHome.png'),
  SERVICE_PHOTO: require('./images/cardServicePhoto.png'),
  WARNING_GREY: require('./images/warning_grey.png'),
  AT_SEA: require('./images/atSea.png'),
  DOWN: require('./images/ChevronDown.png'),
  UP: require('./images/chevronUp.png'),
  DOWNLOAD: require('./images/download.png'),
  RENEWAL: require('./images/renewal.png'),
  INFO: require('./images/info.png'),
  SAVE: require('./images/save.png'),
  ARROW_RIGHT: require('./images/arrowRight.png'),
  AT_PORT: require('./images/atPort.png'),
  VESSEL_PLANS: require('./images/vesselPlans.png'),
  CREW_PHOTO: require('./images/crewPhoto.png'),
  OK: require('./images/ok.png'),
  ATTACHMENT: require('./images/attachment.png'),
  CALENDAR_GREY: require('./images/calendarGrey.png'),
  LOCATION: require('./images/location.png'),
  EYE: require('./images/eye.png'),
  SEA: require('./images/sea.png'),
  APPRAISALS: require('./images/appraisals.png'),
  FILES: require('./images/files.png'),
  MEDICAL: require('./images/medical.png'),
  PROFILE: require('./images/profile.png'),
  DOCUMENT: require('./images/training.png'),
  WHEEL: require('./images/wheel-light.png'),
  MAP_PIN: require('./images/map-pin-light.png'),
  BENEFIT_LOGO: require('./images/benefit.png'),
  BACK: require('./images/BackButton.svg'),
  CLIPBOARD: require('./images/clipboard.png'),
  EXTERNALLINK: require('./images/external-link.png'),
  MENUDOTS: require('./images/menuDots.png'),
  MAP_PIN_TRIP: require('./images/mapPinTrip.png'),
  MAP_PIN_TRIP_XXXL: require('./images/mapPinTripXXXL.png'),
  MAP_PIN_PORT: require('./images/mapPinPort.png'),
  ROUTE: require('./images/route.png'),
  SMALL_ARROW: require('./images/arrowLeft.png'),
  VESSEL_PLACEHOLDER: require('./images/vesselPlaceholder.png'),
  SEAMAN_PLACEHOLDER: require('./images/seamanPlaceholderSquare.png'),
  COMMENTS: require('./images/comments.png'),
  ADD_COMMENT: require('./images/plus.png'),
  FILTER: require('./images/filter.png'),
  STAR: require('./images/star.png'),
  ENVELOPE: require('./images/envelope.png'),
  ENVELOPE_ACTIVE: require('./images/envelope-active.png'),
  LINK: require('./images/link.png'),
  USERS: require('./images/users.png'),
  REPLY: require('./images/reply.png'),
  YELLOW_ARROW: require('./images/yellowArrow.png'),
  BA_LOGO: require('./images/ba_logo.png'),
  AUDIO_VISUALIZATION: require('./images/audio-visualization.gif'),
  CAMERA: require('./images/camera.png'),
  MICROPHONE: require('./images/microphone.png'),
  B_AUDIT_LOGO: require('./images/bAuditLogo.png'),
  B_IN_CHARGE_LOGO: require('./images/bInCharge.png'),
  B_TEAM_LOGO: require('./images/bTeamLogo.png'),
  B_SIGNATURE_LOGO: require('./images/bSignatureLogo.png'),
};

export const LIGHT_THEME_ID = 'light';

export const LIGHT_THEME: Theme = {
  id: LIGHT_THEME_ID,
  color: LIGHT_COLOR_THEME,
  images: LIGHT_THEME_IMAGES,
};
